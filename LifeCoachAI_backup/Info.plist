<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<!-- App Information -->
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>LifeCoach AI</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	
	<!-- App Appearance -->
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	
	<!-- User Interface -->
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
		<string>healthkit</string>
	</array>
	
	<!-- Supported Orientations -->
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	
	<!-- App Transport Security -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
	</dict>
	
	<!-- Privacy Descriptions -->
	<key>NSHealthShareUsageDescription</key>
	<string>LifeCoach AI uses your health data to provide personalized recommendations, track your progress, and help you achieve your wellness goals. This includes steps, heart rate, sleep, and mindfulness data.</string>
	
	<key>NSHealthUpdateUsageDescription</key>
	<string>LifeCoach AI needs to write mindfulness and workout data to Health to track your meditation sessions and activities within the app.</string>
	
	<key>NSUserNotificationsUsageDescription</key>
	<string>LifeCoach AI sends you personalized reminders, goal notifications, and motivational messages to help you stay on track with your wellness journey.</string>
	
	<key>NSCameraUsageDescription</key>
	<string>LifeCoach AI can access your camera to let you take a profile photo for your account.</string>
	
	<key>NSPhotoLibraryUsageDescription</key>
	<string>LifeCoach AI can access your photo library to let you select a profile photo for your account.</string>
	
	<key>NSMicrophoneUsageDescription</key>
	<string>LifeCoach AI uses the microphone for voice notes and guided meditation feedback.</string>
	
	<!-- Background Modes -->
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>audio</string>
	</array>
	
	<!-- App Capabilities -->
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	
	<!-- Status Bar Appearance -->
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	
	<!-- App Store Information -->
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.healthcare-fitness</string>
</dict>
</plist>
