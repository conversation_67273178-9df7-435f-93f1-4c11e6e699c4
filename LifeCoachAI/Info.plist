<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>LifeCoach AI</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict/>
	</dict>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchScreen</key>
	<dict/>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
		<string>healthkit</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>NSHealthShareUsageDescription</key>
	<string>LifeCoach AI needs access to your health data to provide personalized coaching and insights based on your activity, sleep, and other health metrics.</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>LifeCoach AI needs permission to update your health data to track your progress toward your health and wellness goals.</string>
	<key>NSCameraUsageDescription</key>
	<string>LifeCoach AI uses the camera to allow you to take profile pictures and scan QR codes for quick access to features.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>LifeCoach AI uses the microphone for voice notes and guided meditation feedback.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>LifeCoach AI needs access to your photo library to allow you to select profile pictures.</string>
	<key>NSUserNotificationsUsageDescription</key>
	<string>LifeCoach AI sends notifications for reminders, insights, and goal updates to help you stay on track with your wellness journey.</string>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.lifecoach.ai.healthrefresh</string>
		<string>com.lifecoach.ai.insightsgeneration</string>
		<string>com.lifecoach.ai.goalsupdate</string>
	</array>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.healthcare-fitness</string>
</dict>
</plist>
