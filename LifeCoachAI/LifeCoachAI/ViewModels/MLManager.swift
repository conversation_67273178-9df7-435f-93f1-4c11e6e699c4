//
//  MLManager.swift
//  LifeCoachAI
//
//  Created for LifeCoach AI MVP
//

import Foundation
import CoreML // Keep CoreML for using models
import HealthKit
import Combine
import SwiftUI // For TrendDirection, etc. if defined here, or ensure they are globally available

/// Manages machine learning models and generates personalized recommendations
class MLManager: ObservableObject {
    // MARK: - Published Properties
    
    /// Recommendations generated by ML models
    @Published var recommendations: [Recommendation] = []
    
    /// Insights generated from health data
    @Published var insights: [Insight] = [] // Assuming Insight is a defined model
    
    /// Correlation insights between different metrics
    @Published var correlations: [CorrelationInsight] = [] // Assuming CorrelationInsight is a defined model
    
    /// Loading state
    @Published var isLoading: Bool = false
    
    /// Error state
    @Published var error: Error?
    
    /// Today's recommendations
    @Published var todayRecommendations: [Recommendation] = []

    /// High priority recommendations
    @Published var highPriorityRecommendations: [Recommendation] = []

    /// Recommendation categories distribution
    @Published var recommendationCategoryDistribution: [String: Int] = [:]

    /// Recommendation acceptance rate
    @Published var recommendationAcceptanceRate: Double = 0.0
    
    /// Last model update date
    @Published var lastModelUpdateDate: Date?
    
    // MARK: - Private Properties
    
    /// User profile manager reference
    private weak var userProfileManager: UserProfileManager?
    
    /// Health kit manager reference
    private weak var healthKitManager: HealthKitManager?
    
    /// Recommendation model
    private var recommendationModel: MLModel?
    
    /// Insight model
    private var insightModel: MLModel?
    
    /// Subscribers
    private var cancellables = Set<AnyCancellable>()
    
    /// Last recommendation date
    var lastRecommendationDate: Date? // Made public for DashboardView check
    
    /// Last insight generation date
    private var lastInsightDate: Date?
    
    // MARK: - Initialization
    
    init(userProfileManager: UserProfileManager? = nil, healthKitManager: HealthKitManager? = nil) {
        self.userProfileManager = userProfileManager
        self.healthKitManager = healthKitManager
        
        loadModels()
        subscribeToHealthUpdates()
    }
    
    // MARK: - Public Methods
    
    func generateRecommendations() {
        guard !isLoading else { return }
        isLoading = true
        DispatchQueue.global().async { [weak self] in
            guard let self = self else { return }
            Thread.sleep(forTimeInterval: 1.0)
            let newRecommendations = self.generateSampleRecommendations()
            DispatchQueue.main.async {
                self.recommendations = newRecommendations
                self.todayRecommendations = newRecommendations
                self.highPriorityRecommendations = newRecommendations.filter { $0.priority >= 2.0 }
                self.lastRecommendationDate = Date()
                self.isLoading = false
            }
        }
    }
    
    func generateDailyRecommendations() async throws {
        try await Task.sleep(nanoseconds: 1_000_000_000)
        let newRecommendations = generateSampleRecommendations()
        await MainActor.run {
            self.recommendations = newRecommendations
            self.todayRecommendations = newRecommendations
            self.highPriorityRecommendations = newRecommendations.filter { $0.priority >= 2.0 }
            self.lastRecommendationDate = Date()
        }
    }
    
    func generateInsights() {
        guard !isLoading else { return }
        isLoading = true
        DispatchQueue.global().async { [weak self] in
            guard let self = self else { return }
            Thread.sleep(forTimeInterval: 1.5)
            let newInsights = self.generateSampleInsights()
            let newCorrelations = self.generateSampleCorrelations()
            DispatchQueue.main.async {
                self.insights = newInsights
                self.correlations = newCorrelations
                self.lastInsightDate = Date()
                self.isLoading = false
            }
        }
    }
    
    func generateInsightsAsync() async throws {
        try await Task.sleep(nanoseconds: 1_500_000_000)
        let newInsights = generateSampleInsights()
        let newCorrelations = generateSampleCorrelations()
        await MainActor.run {
            self.insights = newInsights
            self.correlations = newCorrelations
            self.lastInsightDate = Date()
        }
    }
    
    func getTrendForMetric(_ metricType: HealthMetricType, timeframe: TimeframeOption = .week) -> TrendDirection {
        let trends: [TrendDirection] = [.up, .down, .stable]
        return trends.randomElement() ?? .stable
    }

    func markRecommendationAsAccepted(id: UUID) {
        if recommendations.firstIndex(where: { $0.id == id }) != nil {
            print("Recommendation \(id) accepted.")
        }
    }

    func markRecommendationAsDeclined(id: UUID) {
        if recommendations.firstIndex(where: { $0.id == id }) != nil {
            print("Recommendation \(id) declined.")
        }
    }
    
    // MARK: - Private Methods
    
    private func loadModels() {
        // Example:
        // do {
        //     let modelURL = Bundle.main.url(forResource: "RecommendationModel", withExtension: "mlmodelc")!
        //     recommendationModel = try MLModel(contentsOf: modelURL)
        // } catch {
        //     print("Error loading recommendation model: \(error)")
        // }
    }
    
    private func subscribeToHealthUpdates() {
        healthKitManager?.$healthData
            .sink { [weak self] _ in
                self?.checkAndGenerateInsights()
            }
            .store(in: &cancellables)
    }
    
    private func checkAndGenerateInsights() {
        guard let lastDate = lastInsightDate else {
            generateInsights()
            return
        }
        let timeInterval = Date().timeIntervalSince(lastDate)
        if timeInterval > 86400 { // 24 hours
            generateInsights()
        }
    }
    
    private func generateSampleRecommendations() -> [Recommendation] {
        [
            Recommendation(id: UUID(), title: "Increase Daily Steps", content: "Try to increase your daily steps to 8,000.", category: "fitness", priority: 2.0, isPremium: false, createdDate: Date()),
            Recommendation(id: UUID(), title: "Improve Sleep Quality", content: "Establish a regular sleep schedule.", category: "sleep", priority: 3.0, isPremium: false, createdDate: Date()),
            Recommendation(id: UUID(), title: "Mindfulness Practice", content: "Try a 5-minute meditation session.", category: "mindfulness", priority: 1.0, isPremium: false, createdDate: Date()),
            Recommendation(id: UUID(), title: "Hydration Reminder", content: "Drink at least 8 glasses of water daily.", category: "nutrition", priority: 2.0, isPremium: false, createdDate: Date()),
            Recommendation(id: UUID(), title: "Advanced Sleep Analysis", content: "Unlock detailed sleep insights.", category: "sleep", priority: 1.0, isPremium: true, createdDate: Date())
        ]
    }
    
    private func generateSampleInsights() -> [Insight] {
        [
            Insight(id: UUID(), title: "Sleep Quality Improving", summary: "Your sleep quality has improved by 15%.", details: "Averaging 7.5 hours of sleep. Keep it up!", category: .sleep, date: Date(), relatedMetrics: [.sleepHours], metricValues: [.sleepHours: 7.5], recommendations: ["Maintain schedule", "Avoid screen time before bed"]),
            Insight(id: UUID(), title: "Activity Level Decreasing", summary: "Activity down 20% this month.", details: "Daily steps dropped. Set a new goal.", category: .activity, date: Date(), relatedMetrics: [.steps, .activeEnergy], metricValues: [.steps: 6400, .activeEnergy: 320], recommendations: ["Goal: 8,000 steps", "Schedule walking breaks"]),
            Insight(id: UUID(), title: "Heart Rate Variability Stable", summary: "HRV stable, good cardiovascular health.", details: "Average HRV of 65ms is healthy. Maintain current practices.", category: .cardio, date: Date(), relatedMetrics: [.heartRate], metricValues: [.heartRate: 65], recommendations: ["Maintain exercise routine", "Continue stress management"]),
            Insight(id: UUID(), title: "Mindfulness Progress", summary: "Mindfulness practice up 25% this week.", details: "Logged 45 mins of mindfulness. Helps reduce stress.", category: .mindfulness, date: Date(), relatedMetrics: [.mindfulMinutes], metricValues: [.mindfulMinutes: 45], recommendations: ["Maintain or increase practice", "Try different meditations"])
        ]
    }
    
    private func generateSampleCorrelations() -> [CorrelationInsight] {
        [
            CorrelationInsight(metricOne: .sleepHours, metricTwo: .steps, correlationType: .positive, strength: 0.7, description: "More sleep correlates with more steps."),
            CorrelationInsight(metricOne: .mindfulMinutes, metricTwo: .heartRate, correlationType: .negative, strength: 0.6, description: "More mindfulness correlates with lower resting heart rate."),
            CorrelationInsight(metricOne: .activeEnergy, metricTwo: .sleepHours, correlationType: .mixed, strength: 0.5, description: "Moderate activity improves sleep; intense late workouts may disrupt it.")
        ]
    }
    
    /// Convert a raw `Recommendation` into the object used by the UI.
    /// For the current MVP the raw `Recommendation` model is already what
    /// views expect, so we simply pass it through. This previously returned a
    /// `RecommendationViewModel`, which no longer exists.
    private func createRecommendation(from recommendation: Recommendation) -> Recommendation {
        return recommendation
    }

    /// Classify activity based on health data (placeholder)
    func classifyActivity(from healthData: [HealthMetric]) -> String {
        // In a real app, this would use an ML model to classify activity
        // For MVP, return a placeholder string
        return "Moderate Activity"
    }

    /// Get recommendations for a specific goal category.
    /// (The UI expects an array of `Recommendation`, *not* the deprecated
    ///  `RecommendationViewModel`.)
    func getRecommendationsForCategory(_ category: LifeCoachAI.GoalCategory) -> [Recommendation] {
        recommendations.filter { $0.category == category.rawValue }
    }

    /// Get high-priority recommendations (priority ≥ 2).
    func getHighPriorityRecommendations() -> [Recommendation] {
        recommendations.filter { $0.priority >= 2.0 }
    }
}
